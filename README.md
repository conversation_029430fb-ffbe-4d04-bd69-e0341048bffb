# Argus Specification

## Description

Argus is a Go application that receives SNMP trap messages, parses the OIDs using snmpTranslate, and sends human-readable messages as output. It serves as a bridge between network monitoring systems and human operators by translating cryptic SNMP notifications into understandable alerts.

## Features

- **SNMP Trap Reception**: Listens for incoming SNMP trap messages
- **OID Translation**: Uses snmpTranslate to convert Object Identifiers to human-readable names
- **Message Processing**: Transforms raw SNMP data into structured, readable output
- **Configurable Output**: Supports multiple output formats and destinations
- **High Performance**: Built in Go for efficient concurrent processing

## Architecture

```mermaid
graph TD
    A[Network Device] -->|SNMP Trap| B[Argus Listener]
    B --> C[Trap Parser]
    C --> D[OID Translator]
    D -->|snmpTranslate| E[MIB Database]
    D --> F[Message Formatter]
    F --> G[Output Handler]
    G --> H[Human Readable Output]
    G --> I[Log Files]
    G --> J[Alert System]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style H fill:#e8f5e8
    style I fill:#fff3e0
    style J fill:#ffebee
```

## Prerequisites

- Go 1.19 or higher
- SNMP utilities (snmp-utils package)
- Access to MIB files for OID translation
- Network access to receive SNMP traps

## Installation

### From Source

```bash
git clone <repository-url>
cd argus
go mod download
go build -o argus ./cmd/argus
```

### Using Docker

```bash
docker build -t argus .
docker run -p 162:162/udp argus
```

## Configuration

Argus can be configured using a YAML configuration file:

```yaml
# config.yaml
server:
  port: 162
  bind_address: "0.0.0.0"

snmp:
  community: "public"
  version: "2c"
  mib_paths:
    - "/usr/share/snmp/mibs"
    - "./mibs"

output:
  format: "json"
  destinations:
    - type: "stdout"
    - type: "file"
      path: "/var/log/argus/traps.log"
    - type: "webhook"
      url: "https://alerts.example.com/webhook"

logging:
  level: "info"
  file: "/var/log/argus/argus.log"
```

## Usage

### Basic Usage

```bash
# Start Argus with default configuration
./argus

# Start with custom configuration file
./argus -config /path/to/config.yaml

# Start with specific port
./argus -port 1162
```

### Docker Usage

```bash
# Run with mounted configuration
docker run -v /path/to/config.yaml:/app/config.yaml -p 162:162/udp argus

# Run with environment variables
docker run -e ARGUS_PORT=162 -e ARGUS_COMMUNITY=public -p 162:162/udp argus
```

## Output Format

Argus transforms SNMP traps into structured, human-readable messages:

### Input (Raw SNMP Trap)

```text
SNMPv2-MIB::sysUpTime.0 = Timeticks: (123456) 0:20:34.56
SNMPv2-MIB::snmpTrapOID.0 = OID: IF-MIB::linkDown
IF-MIB::ifIndex.1 = INTEGER: 1
IF-MIB::ifAdminStatus.1 = INTEGER: up(1)
IF-MIB::ifOperStatus.1 = INTEGER: down(2)
```

### Output (Human-Readable)

```json
{
  "timestamp": "2024-01-15T10:30:45Z",
  "source_ip": "*************",
  "trap_type": "linkDown",
  "severity": "warning",
  "message": "Interface 1 is down (admin status: up, operational status: down)",
  "details": {
    "system_uptime": "0:20:34.56",
    "interface_index": 1,
    "admin_status": "up",
    "operational_status": "down"
  }
}
```

## Development

### Project Structure

```text
argus/
├── cmd/argus/           # Main application entry point
├── internal/
│   ├── config/          # Configuration handling
│   ├── snmp/            # SNMP trap handling
│   ├── translator/      # OID translation logic
│   ├── formatter/       # Message formatting
│   └── output/          # Output handlers
├── pkg/                 # Public packages
├── configs/             # Example configurations
├── docs/                # Documentation
└── tests/               # Test files
```

### Building

```bash
# Build for current platform
go build -o argus ./cmd/argus

# Build for multiple platforms
make build-all

# Run tests
go test ./...

# Run with race detection
go test -race ./...
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:

- Create an issue in the GitHub repository
- Check the [documentation](docs/)
- Review the [FAQ](docs/FAQ.md)

## Roadmap

- [ ] Support for SNMPv3
- [ ] Web UI for monitoring
- [ ] Prometheus metrics integration
- [ ] Custom MIB loading
- [ ] Alert rule engine
- [ ] Multi-tenant support